#!/usr/bin/env python3
"""
Guest Experience Verification Script
Ensures the app provides excellent user experience without requiring authentication.
"""

import os
import sys
from pathlib import Path

def check_guest_mode_implementation():
    """Check if guest mode is properly implemented."""
    print("🔍 Checking guest mode implementation...")
    
    # Check auth provider
    auth_provider_file = Path('front_end/lib/presentation/providers/auth_provider.dart')
    if auth_provider_file.exists():
        content = auth_provider_file.read_text()
        
        checks = [
            ('canUseApp', 'App access control implemented'),
            ('isGuest', 'Guest mode detection implemented'),
            ('AuthState.guest', 'Guest state defined'),
            ('initialize()', 'Auth initialization implemented'),
        ]
        
        for check, message in checks:
            if check in content:
                print(f"  ✅ {message}")
            else:
                print(f"  ❌ {message}")
                return False
    else:
        print("  ❌ Auth provider missing")
        return False
    
    return True

def check_offline_capabilities():
    """Check offline functionality."""
    print("\n🔍 Checking offline capabilities...")
    
    files_to_check = [
        ('front_end/lib/data/repositories/offline_first_quote_repository.dart', 'Offline-first repository'),
        ('front_end/lib/core/services/guest_first_service.dart', 'Guest-first service'),
        ('front_end/lib/core/services/local_storage_service.dart', 'Local storage service'),
        ('front_end/lib/core/services/offline_sync_service.dart', 'Offline sync service'),
    ]
    
    for file_path, description in files_to_check:
        if Path(file_path).exists():
            print(f"  ✅ {description}")
        else:
            print(f"  ❌ {description} missing")
            return False
    
    # Check for fallback quotes
    offline_repo = Path('front_end/lib/data/repositories/offline_first_quote_repository.dart')
    if offline_repo.exists():
        content = offline_repo.read_text()
        if '_fallbackQuotes' in content and 'Steve Jobs' in content:
            print("  ✅ Fallback quotes available")
        else:
            print("  ❌ Fallback quotes missing")
            return False
    
    return True

def check_no_auth_barriers():
    """Check that no screens require authentication."""
    print("\n🔍 Checking for authentication barriers...")
    
    # Common patterns that might block guest users
    blocking_patterns = [
        'if (!isAuthenticated)',
        'if (user == null)',
        'requireAuth',
        'mustBeLoggedIn',
        'authRequired',
    ]
    
    # Check main screens
    screens_dir = Path('front_end/lib/presentation/screens')
    if screens_dir.exists():
        dart_files = list(screens_dir.glob('*.dart'))
        
        for dart_file in dart_files:
            content = dart_file.read_text()
            
            for pattern in blocking_patterns:
                if pattern in content:
                    print(f"  ⚠️  Potential auth barrier in {dart_file.name}: {pattern}")
        
        print(f"  ✅ Checked {len(dart_files)} screen files")
    
    return True

def check_local_data_availability():
    """Check that local data is available for offline use."""
    print("\n🔍 Checking local data availability...")
    
    guest_service = Path('front_end/lib/core/services/guest_first_service.dart')
    if guest_service.exists():
        content = guest_service.read_text()
        
        data_checks = [
            ('_cacheInitialQuotes', 'Initial quotes caching'),
            ('_cacheCategories', 'Categories caching'),
            ('_setDefaultPreferences', 'Default preferences'),
            ('getCachedQuotes', 'Quote retrieval method'),
            ('getCachedCategories', 'Category retrieval method'),
        ]
        
        for check, description in data_checks:
            if check in content:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} missing")
                return False
    else:
        print("  ❌ Guest service missing")
        return False
    
    return True

def check_widget_offline_support():
    """Check that home widget works offline."""
    print("\n🔍 Checking widget offline support...")
    
    widget_file = Path('front_end/android/app/src/main/kotlin/com/example/daily_motivator/DailyMotivatorWidget.kt')
    if widget_file.exists():
        content = widget_file.read_text()
        
        if 'SharedPreferences' in content:
            print("  ✅ Widget uses local storage")
        else:
            print("  ⚠️  Widget might not work offline")
        
        if 'isNullOrEmpty' in content:
            print("  ✅ Widget handles null values safely")
        else:
            print("  ❌ Widget null safety issues")
            return False
    else:
        print("  ❌ Widget implementation missing")
        return False
    
    return True

def check_notification_offline_support():
    """Check that notifications work offline."""
    print("\n🔍 Checking notification offline support...")
    
    notification_service = Path('front_end/lib/core/services/notification_service.dart')
    if notification_service.exists():
        content = notification_service.read_text()
        
        if 'SharedPreferences' in content or 'local' in content.lower():
            print("  ✅ Notifications can work offline")
        else:
            print("  ⚠️  Notifications might require internet")
        
        if 'flutter_local_notifications' in content:
            print("  ✅ Local notifications implemented")
        else:
            print("  ❌ Local notifications missing")
            return False
    else:
        print("  ❌ Notification service missing")
        return False
    
    return True

def check_dependency_injection():
    """Check that DI is configured for offline-first experience."""
    print("\n🔍 Checking dependency injection...")
    
    di_file = Path('front_end/lib/core/di/injection_container.dart')
    if di_file.exists():
        content = di_file.read_text()
        
        if 'OfflineFirstQuoteRepository' in content:
            print("  ✅ Offline-first repository configured")
        else:
            print("  ❌ Offline-first repository not configured")
            return False
        
        if 'GuestFirstService' in content or 'LocalStorageService' in content:
            print("  ✅ Guest services configured")
        else:
            print("  ⚠️  Guest services might not be configured")
    else:
        print("  ❌ Dependency injection missing")
        return False
    
    return True

def check_splash_screen_initialization():
    """Check that splash screen initializes guest mode."""
    print("\n🔍 Checking splash screen initialization...")
    
    splash_file = Path('front_end/lib/presentation/screens/splash_screen.dart')
    if splash_file.exists():
        content = splash_file.read_text()
        
        if 'GuestFirstService' in content:
            print("  ✅ Guest-first service initialized")
        else:
            print("  ❌ Guest-first service not initialized")
            return False
        
        if 'initializeGuestMode' in content:
            print("  ✅ Guest mode initialization called")
        else:
            print("  ❌ Guest mode initialization missing")
            return False
    else:
        print("  ❌ Splash screen missing")
        return False
    
    return True

def main():
    """Main verification function."""
    print("🚀 Guest Experience Verification")
    print("=" * 50)
    print("Ensuring users have excellent experience without mandatory sign-in")
    print()
    
    checks = [
        check_guest_mode_implementation(),
        check_offline_capabilities(),
        check_no_auth_barriers(),
        check_local_data_availability(),
        check_widget_offline_support(),
        check_notification_offline_support(),
        check_dependency_injection(),
        check_splash_screen_initialization(),
    ]
    
    passed_checks = sum(checks)
    total_checks = len(checks)
    
    print(f"\n📊 Results: {passed_checks}/{total_checks} checks passed")
    
    if all(checks):
        print("\n✅ EXCELLENT! Your app provides a perfect guest experience!")
        print("\n🎉 Key Features Working:")
        print("  • Guest mode is default - no mandatory sign-in")
        print("  • All screens accessible without authentication")
        print("  • Rich offline functionality with local quotes")
        print("  • Home widget works without internet")
        print("  • Notifications work offline")
        print("  • Local data storage for preferences")
        print("  • Smooth fallback when API unavailable")
        print("\n👤 User Experience:")
        print("  • Users can enjoy all features immediately")
        print("  • No internet connection required")
        print("  • Optional sign-in only for data sync")
        print("  • Seamless offline/online transitions")
        
        return True
    else:
        print(f"\n⚠️  {total_checks - passed_checks} issues found. Please fix them for optimal guest experience.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
