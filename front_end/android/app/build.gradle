plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.example.daily_motivator"
    compileSdk = 35
    // ndkVersion = "25.1.8937393"
    ndkVersion = "26.1.10909125"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
        freeCompilerArgs += [
            "-Xuse-k2"
        ]
    }
    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.daily_motivator"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21  // Required for notifications
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            debuggable true
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
            minifyEnabled true
            shrinkResources true
        }
    }
}

flutter {
    source = "../.."
}
dependencies {
    // coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
 
 
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'  // ✅ Match plugin
   
 

}
