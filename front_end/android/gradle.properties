# Java heap settings
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError

# AndroidX settings
android.useAndroidX=true
android.enableJetifier=true

# Build performance
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configureondemand=true
org.gradle.caching=true
org.gradle.workers.max=2
android.enableR8.fullMode=false

# Java home (ensure this is the correct path for your system)
org.gradle.java.home=/usr/lib/jvm/java-17-openjdk-amd64

# Network timeouts
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
systemProp.https.connectionTimeout=300000
systemProp.https.socketTimeout=300000
