buildscript {
    ext.kotlin_version = '1.9.0' // ✅ Defined inside buildscript

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://jitpack.io' }
    }
}

rootProject.buildDir = "../build"

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"

    // ✅ Set Kotlin JVM target to 17
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "17"
        }
    }

    // ✅ Safely handle maxHeapSize
    tasks.withType(JavaCompile).configureEach {
        if (options.hasProperty("forkOptions") && options.forkOptions.hasProperty("maxHeapSize")) {
            options.forkOptions.maxHeapSize = null
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
