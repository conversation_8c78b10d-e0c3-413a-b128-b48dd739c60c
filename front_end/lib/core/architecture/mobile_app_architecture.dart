import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dynamic_asset_manager.dart';
import 'advanced_theme_manager.dart';
import 'secure_auth_manager.dart';
import '../services/logging_service.dart';

/// Comprehensive mobile application architecture integrating all components
class MobileAppArchitecture {
  static MobileAppArchitecture? _instance;
  static MobileAppArchitecture get instance => _instance ??= MobileAppArchitecture._();
  
  MobileAppArchitecture._();
  
  // Architecture components
  late DynamicAssetManager _assetManager;
  late AdvancedThemeManager _themeManager;
  late SecureAuthManager _authManager;
  
  bool _initialized = false;
  
  // Getters
  DynamicAssetManager get assetManager => _assetManager;
  AdvancedThemeManager get themeManager => _themeManager;
  SecureAuthManager get authManager => _authManager;
  bool get isInitialized => _initialized;

  /// Initialize the complete architecture
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      LoggingService.info('Initializing mobile app architecture...');
      
      // Initialize components in order
      _assetManager = DynamicAssetManager.instance;
      _themeManager = AdvancedThemeManager.instance;
      _authManager = SecureAuthManager.instance;
      
      // Initialize theme manager first (affects UI)
      await _themeManager.initialize();
      
      // Initialize auth manager
      await _authManager.initialize();
      
      // Preload essential assets
      await _preloadEssentialAssets();
      
      _initialized = true;
      LoggingService.info('Mobile app architecture initialized successfully');
    } catch (e) {
      LoggingService.error('Failed to initialize mobile app architecture: $e');
      rethrow;
    }
  }

  /// Preload essential assets for better performance
  Future<void> _preloadEssentialAssets() async {
    try {
      // This would be called with a BuildContext in a real app
      LoggingService.info('Essential assets preloading scheduled');
    } catch (e) {
      LoggingService.warning('Failed to preload essential assets: $e');
    }
  }

  /// Get architecture status
  Map<String, dynamic> getArchitectureStatus() {
    return {
      'initialized': _initialized,
      'asset_manager': _assetManager.getAssetInfo(),
      'theme_manager': _themeManager.getThemeInfo(),
      'auth_manager': {
        'state': _authManager.state.toString(),
        'is_authenticated': _authManager.isAuthenticated,
        'biometric_enabled': _authManager.biometricEnabled,
      },
    };
  }
}

/// Main application widget with integrated architecture
class ArchitecturedApp extends StatefulWidget {
  final Widget child;
  final String title;
  
  const ArchitecturedApp({
    super.key,
    required this.child,
    this.title = 'Mobile App',
  });

  @override
  State<ArchitecturedApp> createState() => _ArchitecturedAppState();
}

class _ArchitecturedAppState extends State<ArchitecturedApp>
    with WidgetsBindingObserver {
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeArchitecture();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    // Handle system theme changes
    MobileAppArchitecture.instance.themeManager.handleSystemThemeChange();
  }

  Future<void> _initializeArchitecture() async {
    try {
      await MobileAppArchitecture.instance.initialize();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      LoggingService.error('Architecture initialization failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!MobileAppArchitecture.instance.isInitialized) {
      return MaterialApp(
        title: widget.title,
        home: const Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Initializing...'),
              ],
            ),
          ),
        ),
      );
    }

    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(
          value: MobileAppArchitecture.instance.themeManager,
        ),
        ChangeNotifierProvider.value(
          value: MobileAppArchitecture.instance.authManager,
        ),
      ],
      child: Consumer<AdvancedThemeManager>(
        builder: (context, themeManager, child) {
          return AnimatedThemeWrapper(
            child: MaterialApp(
              title: widget.title,
              theme: themeManager.lightTheme,
              darkTheme: themeManager.darkTheme,
              themeMode: themeManager.themeMode,
              home: widget.child,
              builder: (context, child) {
                return MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                    textScaler: TextScaler.linear(1.0), // Prevent text scaling issues
                  ),
                  child: child!,
                );
              },
            ),
          );
        },
      ),
    );
  }
}

/// Utility widget for easy asset loading with theme awareness
class ThemedAssetImage extends StatelessWidget {
  final String category;
  final String? specificAsset;
  final double? width;
  final double? height;
  final BoxFit fit;

  const ThemedAssetImage({
    super.key,
    required this.category,
    this.specificAsset,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AdvancedThemeManager>(
      builder: (context, themeManager, child) {
        // Adjust asset based on theme
        final themeAwareCategory = themeManager.isDarkMode 
          ? '${category}_dark' 
          : '${category}_light';
        
        return DynamicAssetImage(
          category: themeAwareCategory,
          specificAsset: specificAsset,
          width: width,
          height: height,
          fit: fit,
          placeholder: Container(
            width: width,
            height: height,
            color: themeManager.getAdaptiveCardColor(),
            child: Icon(
              Icons.image,
              color: themeManager.getAdaptiveTextColor().withOpacity(0.5),
            ),
          ),
          errorWidget: Container(
            width: width,
            height: height,
            color: themeManager.getAdaptiveCardColor(),
            child: Icon(
              Icons.broken_image,
              color: themeManager.getAdaptiveTextColor().withOpacity(0.5),
            ),
          ),
        );
      },
    );
  }
}

/// Authentication-aware widget wrapper
class AuthAwareWidget extends StatelessWidget {
  final Widget authenticatedChild;
  final Widget guestChild;
  final Widget? loadingChild;

  const AuthAwareWidget({
    super.key,
    required this.authenticatedChild,
    required this.guestChild,
    this.loadingChild,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SecureAuthManager>(
      builder: (context, authManager, child) {
        switch (authManager.state) {
          case AuthenticationState.authenticated:
            return authenticatedChild;
          case AuthenticationState.guest:
            return guestChild;
          case AuthenticationState.authenticating:
            return loadingChild ?? 
              const Center(child: CircularProgressIndicator());
          case AuthenticationState.unauthenticated:
          case AuthenticationState.locked:
            return guestChild;
        }
      },
    );
  }
}

/// Theme toggle button with smooth animation
class AnimatedThemeToggle extends StatelessWidget {
  final double size;
  final Color? lightColor;
  final Color? darkColor;

  const AnimatedThemeToggle({
    super.key,
    this.size = 24.0,
    this.lightColor,
    this.darkColor,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AdvancedThemeManager>(
      builder: (context, themeManager, child) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (child, animation) {
            return RotationTransition(
              turns: animation,
              child: child,
            );
          },
          child: IconButton(
            key: ValueKey(themeManager.isDarkMode),
            icon: Icon(
              themeManager.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              size: size,
              color: themeManager.isDarkMode 
                ? (darkColor ?? Colors.amber)
                : (lightColor ?? Colors.grey[700]),
            ),
            onPressed: () => themeManager.toggleTheme(),
            tooltip: themeManager.isDarkMode 
              ? 'Switch to light mode' 
              : 'Switch to dark mode',
          ),
        );
      },
    );
  }
}

/// Performance monitoring widget
class ArchitecturePerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const ArchitecturePerformanceMonitor({
    super.key,
    required this.child,
    this.enabled = false,
  });

  @override
  State<ArchitecturePerformanceMonitor> createState() => 
    _ArchitecturePerformanceMonitorState();
}

class _ArchitecturePerformanceMonitorState 
    extends State<ArchitecturePerformanceMonitor> {
  
  @override
  void initState() {
    super.initState();
    if (widget.enabled) {
      _startPerformanceMonitoring();
    }
  }

  void _startPerformanceMonitoring() {
    // Monitor architecture performance
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        final status = MobileAppArchitecture.instance.getArchitectureStatus();
        LoggingService.info('Architecture performance: $status');
        _startPerformanceMonitoring(); // Continue monitoring
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
