import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../services/storage_service.dart';
import '../services/logging_service.dart';

/// Secure authentication manager with encrypted storage and biometric support
class SecureAuthManager extends ChangeNotifier {
  static SecureAuthManager? _instance;
  static SecureAuthManager get instance => _instance ??= SecureAuthManager._();
  
  SecureAuthManager._();
  
  // Secure storage instance
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  
  // Authentication state
  AuthenticationState _state = AuthenticationState.unauthenticated;
  UserProfile? _currentUser;
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiry;
  
  // Security settings
  bool _biometricEnabled = false;
  bool _autoLockEnabled = true;
  Duration _autoLockDuration = const Duration(minutes: 5);
  DateTime? _lastActivity;
  
  // Getters
  AuthenticationState get state => _state;
  UserProfile? get currentUser => _currentUser;
  bool get isAuthenticated => _state == AuthenticationState.authenticated;
  bool get isGuest => _state == AuthenticationState.guest;
  bool get biometricEnabled => _biometricEnabled;
  bool get autoLockEnabled => _autoLockEnabled;
  Duration get autoLockDuration => _autoLockDuration;

  /// Initialize authentication manager
  Future<void> initialize() async {
    try {
      // Load security settings
      await _loadSecuritySettings();
      
      // Check for existing session
      await _checkExistingSession();
      
      // Start auto-lock timer if enabled
      if (_autoLockEnabled) {
        _startAutoLockTimer();
      }
      
      LoggingService.info('Secure auth manager initialized');
    } catch (e) {
      LoggingService.error('Failed to initialize auth manager: $e');
      _state = AuthenticationState.guest;
      notifyListeners();
    }
  }

  /// Authenticate user with email/password
  Future<AuthResult> authenticateWithPassword({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _state = AuthenticationState.authenticating;
      notifyListeners();
      
      // Hash password securely
      final hashedPassword = _hashPassword(password);
      
      // Simulate API call (replace with actual authentication)
      await Future.delayed(const Duration(seconds: 1));
      
      // For demo purposes, accept any email/password combination
      if (email.isNotEmpty && password.length >= 6) {
        final tokens = await _generateTokens();
        final user = UserProfile(
          id: _generateUserId(),
          email: email,
          name: email.split('@').first,
          createdAt: DateTime.now(),
        );
        
        await _storeAuthData(tokens, user, rememberMe);
        
        _state = AuthenticationState.authenticated;
        _currentUser = user;
        _updateActivity();
        notifyListeners();
        
        LoggingService.info('User authenticated successfully: ${user.email}');
        return AuthResult.success();
      } else {
        _state = AuthenticationState.unauthenticated;
        notifyListeners();
        return AuthResult.failure('Invalid credentials');
      }
    } catch (e) {
      _state = AuthenticationState.unauthenticated;
      notifyListeners();
      LoggingService.error('Authentication failed: $e');
      return AuthResult.failure('Authentication failed: $e');
    }
  }

  /// Authenticate with biometrics
  Future<AuthResult> authenticateWithBiometrics() async {
    if (!_biometricEnabled) {
      return AuthResult.failure('Biometric authentication not enabled');
    }
    
    try {
      _state = AuthenticationState.authenticating;
      notifyListeners();
      
      // Check if biometric data exists
      final biometricData = await _secureStorage.read(key: 'biometric_auth_data');
      if (biometricData == null) {
        _state = AuthenticationState.unauthenticated;
        notifyListeners();
        return AuthResult.failure('No biometric data found');
      }
      
      // Simulate biometric authentication
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Load stored user data
      final userData = await _secureStorage.read(key: 'user_profile');
      if (userData != null) {
        final user = UserProfile.fromJson(jsonDecode(userData));
        _currentUser = user;
        _state = AuthenticationState.authenticated;
        _updateActivity();
        notifyListeners();
        
        LoggingService.info('Biometric authentication successful');
        return AuthResult.success();
      } else {
        _state = AuthenticationState.unauthenticated;
        notifyListeners();
        return AuthResult.failure('User data not found');
      }
    } catch (e) {
      _state = AuthenticationState.unauthenticated;
      notifyListeners();
      LoggingService.error('Biometric authentication failed: $e');
      return AuthResult.failure('Biometric authentication failed');
    }
  }

  /// Enter guest mode
  Future<void> enterGuestMode() async {
    try {
      _state = AuthenticationState.guest;
      _currentUser = null;
      _accessToken = null;
      _refreshToken = null;
      _tokenExpiry = null;
      
      notifyListeners();
      LoggingService.info('Entered guest mode');
    } catch (e) {
      LoggingService.error('Failed to enter guest mode: $e');
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      // Clear secure storage
      await _secureStorage.deleteAll();
      
      // Clear regular storage
      await StorageService.removeData('auth_remember_me');
      
      // Reset state
      _state = AuthenticationState.unauthenticated;
      _currentUser = null;
      _accessToken = null;
      _refreshToken = null;
      _tokenExpiry = null;
      
      notifyListeners();
      LoggingService.info('User signed out successfully');
    } catch (e) {
      LoggingService.error('Sign out failed: $e');
    }
  }

  /// Enable/disable biometric authentication
  Future<bool> setBiometricEnabled(bool enabled) async {
    try {
      if (enabled && _currentUser != null) {
        // Store biometric auth data
        await _secureStorage.write(
          key: 'biometric_auth_data',
          value: _generateBiometricToken(),
        );
        
        // Store user profile for biometric access
        await _secureStorage.write(
          key: 'user_profile',
          value: jsonEncode(_currentUser!.toJson()),
        );
      } else if (!enabled) {
        // Remove biometric data
        await _secureStorage.delete(key: 'biometric_auth_data');
        await _secureStorage.delete(key: 'user_profile');
      }
      
      _biometricEnabled = enabled;
      await _saveSecuritySettings();
      notifyListeners();
      
      LoggingService.info('Biometric authentication ${enabled ? 'enabled' : 'disabled'}');
      return true;
    } catch (e) {
      LoggingService.error('Failed to set biometric enabled: $e');
      return false;
    }
  }

  /// Set auto-lock settings
  Future<void> setAutoLockSettings({
    required bool enabled,
    Duration? duration,
  }) async {
    try {
      _autoLockEnabled = enabled;
      if (duration != null) {
        _autoLockDuration = duration;
      }
      
      await _saveSecuritySettings();
      
      if (enabled) {
        _startAutoLockTimer();
      }
      
      notifyListeners();
      LoggingService.info('Auto-lock settings updated: enabled=$enabled, duration=${_autoLockDuration.inMinutes}min');
    } catch (e) {
      LoggingService.error('Failed to set auto-lock settings: $e');
    }
  }

  /// Update user activity (reset auto-lock timer)
  void _updateActivity() {
    _lastActivity = DateTime.now();
    if (_autoLockEnabled) {
      _startAutoLockTimer();
    }
  }

  /// Start auto-lock timer
  void _startAutoLockTimer() {
    if (!_autoLockEnabled || !isAuthenticated) return;
    
    Future.delayed(_autoLockDuration, () {
      if (_lastActivity != null &&
          DateTime.now().difference(_lastActivity!) >= _autoLockDuration &&
          isAuthenticated) {
        _autoLock();
      }
    });
  }

  /// Auto-lock the app
  void _autoLock() {
    if (_biometricEnabled) {
      _state = AuthenticationState.locked;
    } else {
      _state = AuthenticationState.unauthenticated;
      _currentUser = null;
    }
    notifyListeners();
    LoggingService.info('App auto-locked due to inactivity');
  }

  /// Hash password securely
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Generate secure tokens
  Future<AuthTokens> _generateTokens() async {
    final random = Random.secure();
    final accessToken = base64.encode(List.generate(32, (_) => random.nextInt(256)));
    final refreshToken = base64.encode(List.generate(32, (_) => random.nextInt(256)));
    
    return AuthTokens(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: DateTime.now().add(const Duration(hours: 1)),
    );
  }

  /// Generate user ID
  String _generateUserId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Generate biometric token
  String _generateBiometricToken() {
    final random = Random.secure();
    return base64.encode(List.generate(32, (_) => random.nextInt(256)));
  }

  /// Store authentication data securely
  Future<void> _storeAuthData(AuthTokens tokens, UserProfile user, bool rememberMe) async {
    await _secureStorage.write(key: 'access_token', value: tokens.accessToken);
    await _secureStorage.write(key: 'refresh_token', value: tokens.refreshToken);
    await _secureStorage.write(key: 'token_expiry', value: tokens.expiresAt.toIso8601String());
    
    if (rememberMe) {
      await _secureStorage.write(key: 'user_profile', value: jsonEncode(user.toJson()));
      await StorageService.saveData('auth_remember_me', true);
    }
    
    _accessToken = tokens.accessToken;
    _refreshToken = tokens.refreshToken;
    _tokenExpiry = tokens.expiresAt;
  }

  /// Check for existing session
  Future<void> _checkExistingSession() async {
    try {
      final rememberMe = StorageService.loadData<bool>('auth_remember_me', defaultValue: false) ?? false;
      
      if (rememberMe) {
        final userData = await _secureStorage.read(key: 'user_profile');
        final tokenData = await _secureStorage.read(key: 'access_token');
        
        if (userData != null && tokenData != null) {
          _currentUser = UserProfile.fromJson(jsonDecode(userData));
          _accessToken = tokenData;
          _state = AuthenticationState.authenticated;
          _updateActivity();
          LoggingService.info('Restored existing session');
        }
      }
    } catch (e) {
      LoggingService.error('Failed to check existing session: $e');
    }
    
    if (_state != AuthenticationState.authenticated) {
      _state = AuthenticationState.guest;
    }
  }

  /// Load security settings
  Future<void> _loadSecuritySettings() async {
    try {
      _biometricEnabled = StorageService.loadData<bool>('biometric_enabled', defaultValue: false) ?? false;
      _autoLockEnabled = StorageService.loadData<bool>('auto_lock_enabled', defaultValue: true) ?? true;
      final lockMinutes = StorageService.loadData<int>('auto_lock_minutes', defaultValue: 5) ?? 5;
      _autoLockDuration = Duration(minutes: lockMinutes);
    } catch (e) {
      LoggingService.error('Failed to load security settings: $e');
    }
  }

  /// Save security settings
  Future<void> _saveSecuritySettings() async {
    try {
      await StorageService.saveData('biometric_enabled', _biometricEnabled);
      await StorageService.saveData('auto_lock_enabled', _autoLockEnabled);
      await StorageService.saveData('auto_lock_minutes', _autoLockDuration.inMinutes);
    } catch (e) {
      LoggingService.error('Failed to save security settings: $e');
    }
  }
}

// Supporting classes
enum AuthenticationState {
  unauthenticated,
  authenticating,
  authenticated,
  guest,
  locked,
}

class UserProfile {
  final String id;
  final String email;
  final String name;
  final DateTime createdAt;

  UserProfile({
    required this.id,
    required this.email,
    required this.name,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'email': email,
    'name': name,
    'created_at': createdAt.toIso8601String(),
  };

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
    id: json['id'],
    email: json['email'],
    name: json['name'],
    createdAt: DateTime.parse(json['created_at']),
  );
}

class AuthTokens {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;

  AuthTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
  });
}

class AuthResult {
  final bool success;
  final String? error;

  AuthResult._(this.success, this.error);

  factory AuthResult.success() => AuthResult._(true, null);
  factory AuthResult.failure(String error) => AuthResult._(false, error);
}
