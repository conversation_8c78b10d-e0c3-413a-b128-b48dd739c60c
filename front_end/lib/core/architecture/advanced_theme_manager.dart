import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/storage_service.dart';
import '../services/logging_service.dart';

/// Advanced theme manager with seamless light/dark transitions and animations
class AdvancedThemeManager extends ChangeNotifier {
  static AdvancedThemeManager? _instance;
  static AdvancedThemeManager get instance => _instance ??= AdvancedThemeManager._();
  
  AdvancedThemeManager._();
  
  // Theme state
  ThemeMode _themeMode = ThemeMode.system;
  bool _isTransitioning = false;
  Duration _transitionDuration = const Duration(milliseconds: 300);
  
  // Theme data
  late ThemeData _lightTheme;
  late ThemeData _darkTheme;
  late ThemeData _currentTheme;
  
  // Animation controllers (managed externally)
  AnimationController? _themeAnimationController;
  Animation<double>? _themeAnimation;
  
  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get isTransitioning => _isTransitioning;
  Duration get transitionDuration => _transitionDuration;
  ThemeData get lightTheme => _lightTheme;
  ThemeData get darkTheme => _darkTheme;
  ThemeData get currentTheme => _currentTheme;
  bool get isDarkMode => _themeMode == ThemeMode.dark || 
    (_themeMode == ThemeMode.system && _isSystemDark());

  /// Initialize theme manager
  Future<void> initialize() async {
    try {
      // Load saved theme preference
      final savedTheme = StorageService.loadData<String>('theme_mode', defaultValue: 'system');
      _themeMode = _parseThemeMode(savedTheme ?? 'system');
      
      // Initialize themes
      _initializeThemes();
      _updateCurrentTheme();
      
      LoggingService.info('Theme manager initialized with mode: $_themeMode');
    } catch (e) {
      LoggingService.error('Failed to initialize theme manager: $e');
      _themeMode = ThemeMode.system;
      _initializeThemes();
      _updateCurrentTheme();
    }
  }

  /// Initialize light and dark themes
  void _initializeThemes() {
    _lightTheme = ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4ECDC4),
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
    );

    _darkTheme = ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4ECDC4),
        brightness: Brightness.dark,
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[800],
      ),
    );
  }

  /// Set theme mode with smooth transition
  Future<void> setThemeMode(ThemeMode mode, {bool animate = true}) async {
    if (_themeMode == mode) return;
    
    try {
      if (animate) {
        await _animateThemeTransition(() {
          _themeMode = mode;
          _updateCurrentTheme();
        });
      } else {
        _themeMode = mode;
        _updateCurrentTheme();
      }
      
      // Save preference
      await StorageService.saveData('theme_mode', mode.toString().split('.').last);
      
      // Update system UI overlay
      _updateSystemUIOverlay();
      
      notifyListeners();
      LoggingService.info('Theme mode changed to: $mode');
    } catch (e) {
      LoggingService.error('Failed to set theme mode: $e');
    }
  }

  /// Animate theme transition
  Future<void> _animateThemeTransition(VoidCallback themeChange) async {
    if (_themeAnimationController == null) {
      // If no animation controller, just change immediately
      themeChange();
      return;
    }
    
    _isTransitioning = true;
    notifyListeners();
    
    try {
      // Animate out
      await _themeAnimationController!.reverse();
      
      // Change theme
      themeChange();
      
      // Animate in
      await _themeAnimationController!.forward();
    } finally {
      _isTransitioning = false;
      notifyListeners();
    }
  }

  /// Set animation controller for theme transitions
  void setAnimationController(AnimationController controller) {
    _themeAnimationController = controller;
    _themeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: controller, curve: Curves.easeInOut),
    );
  }

  /// Update current theme based on mode
  void _updateCurrentTheme() {
    switch (_themeMode) {
      case ThemeMode.light:
        _currentTheme = _lightTheme;
        break;
      case ThemeMode.dark:
        _currentTheme = _darkTheme;
        break;
      case ThemeMode.system:
        _currentTheme = _isSystemDark() ? _darkTheme : _lightTheme;
        break;
    }
  }

  /// Check if system is in dark mode
  bool _isSystemDark() {
    return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
  }

  /// Update system UI overlay style
  void _updateSystemUIOverlay() {
    final isDark = isDarkMode;
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: isDark ? Colors.black : Colors.white,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      ),
    );
  }

  /// Parse theme mode from string
  ThemeMode _parseThemeMode(String mode) {
    switch (mode.toLowerCase()) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final newMode = isDarkMode ? ThemeMode.light : ThemeMode.dark;
    await setThemeMode(newMode);
  }

  /// Get theme-aware color
  Color getThemeColor(Color lightColor, Color darkColor) {
    return isDarkMode ? darkColor : lightColor;
  }

  /// Get adaptive text color
  Color getAdaptiveTextColor() {
    return isDarkMode ? Colors.white : Colors.black;
  }

  /// Get adaptive background color
  Color getAdaptiveBackgroundColor() {
    return isDarkMode ? Colors.grey[900]! : Colors.white;
  }

  /// Get adaptive card color
  Color getAdaptiveCardColor() {
    return isDarkMode ? Colors.grey[800]! : Colors.white;
  }

  /// Listen to system theme changes
  void handleSystemThemeChange() {
    if (_themeMode == ThemeMode.system) {
      _updateCurrentTheme();
      _updateSystemUIOverlay();
      notifyListeners();
    }
  }

  /// Get theme info for debugging
  Map<String, dynamic> getThemeInfo() {
    return {
      'current_mode': _themeMode.toString(),
      'is_dark': isDarkMode,
      'is_transitioning': _isTransitioning,
      'transition_duration': _transitionDuration.inMilliseconds,
      'system_brightness': WidgetsBinding.instance.platformDispatcher.platformBrightness.toString(),
    };
  }
}

/// Widget that provides smooth theme transitions
class AnimatedThemeWrapper extends StatefulWidget {
  final Widget child;
  final Duration duration;

  const AnimatedThemeWrapper({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedThemeWrapper> createState() => _AnimatedThemeWrapperState();
}

class _AnimatedThemeWrapperState extends State<AnimatedThemeWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Set animation controller in theme manager
    AdvancedThemeManager.instance.setAnimationController(_controller);

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _animation,
          child: widget.child,
        );
      },
    );
  }
}

/// Advanced theme transition animations
class ThemeTransitionAnimations {
  static const Duration defaultDuration = Duration(milliseconds: 300);

  /// Slide transition for theme changes
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    Axis direction = Axis.horizontal,
  }) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: direction == Axis.horizontal ? const Offset(1.0, 0.0) : const Offset(0.0, 1.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(parent: animation, curve: Curves.easeInOut)),
      child: child,
    );
  }

  /// Scale transition for theme changes
  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return ScaleTransition(
      scale: Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(parent: animation, curve: Curves.elasticOut),
      ),
      child: child,
    );
  }

  /// Rotation transition for theme changes
  static Widget rotationTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return RotationTransition(
      turns: Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: animation, curve: Curves.easeInOut),
      ),
      child: child,
    );
  }

  /// Combined transition effects
  static Widget combinedTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return FadeTransition(
      opacity: animation,
      child: ScaleTransition(
        scale: Tween<double>(begin: 0.9, end: 1.0).animate(
          CurvedAnimation(parent: animation, curve: Curves.easeOut),
        ),
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 0.1),
            end: Offset.zero,
          ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut)),
          child: child,
        ),
      ),
    );
  }
}
