import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../services/logging_service.dart';
import '../services/storage_service.dart';

/// Comprehensive dynamic asset manager with programmatic fallback mechanisms
class DynamicAssetManager {
  static DynamicAssetManager? _instance;
  static DynamicAssetManager get instance => _instance ??= DynamicAssetManager._();
  
  DynamicAssetManager._();
  
  // Asset cache for performance
  final Map<String, ImageProvider> _imageCache = {};
  final Map<String, String> _assetPathCache = {};
  
  // Fallback asset configurations
  static const Map<String, List<String>> _assetFallbacks = {
    'motivation': [
      'assets/images/motivation_1.jpg',
      'assets/images/motivation_2.jpg',
      'assets/images/default_motivation.jpg',
      'assets/images/placeholder.jpg',
    ],
    'inspiration': [
      'assets/images/inspiration_1.jpg',
      'assets/images/inspiration_2.jpg',
      'assets/images/default_inspiration.jpg',
      'assets/images/placeholder.jpg',
    ],
    'success': [
      'assets/images/success_1.jpg',
      'assets/images/success_2.jpg',
      'assets/images/default_success.jpg',
      'assets/images/placeholder.jpg',
    ],
    'default': [
      'assets/images/default_background.jpg',
      'assets/images/placeholder.jpg',
    ],
  };
  
  // Generated fallback colors for ultimate fallback
  static const Map<String, Color> _colorFallbacks = {
    'motivation': Color(0xFF4ECDC4),
    'inspiration': Color(0xFF45B7D1),
    'success': Color(0xFF96CEB4),
    'default': Color(0xFF6C5CE7),
  };

  /// Get image with comprehensive fallback strategy
  Future<ImageProvider> getImage({
    required String category,
    String? specificAsset,
    bool useCache = true,
  }) async {
    final cacheKey = '${category}_${specificAsset ?? 'default'}';
    
    // Return cached image if available
    if (useCache && _imageCache.containsKey(cacheKey)) {
      return _imageCache[cacheKey]!;
    }
    
    ImageProvider? imageProvider;
    
    try {
      // Strategy 1: Try specific asset if provided
      if (specificAsset != null) {
        imageProvider = await _tryLoadAsset(specificAsset);
        if (imageProvider != null) {
          LoggingService.info('Loaded specific asset: $specificAsset');
          return _cacheAndReturn(cacheKey, imageProvider);
        }
      }
      
      // Strategy 2: Try category-specific fallbacks
      final fallbacks = _assetFallbacks[category] ?? _assetFallbacks['default']!;
      for (final assetPath in fallbacks) {
        imageProvider = await _tryLoadAsset(assetPath);
        if (imageProvider != null) {
          LoggingService.info('Loaded fallback asset: $assetPath');
          return _cacheAndReturn(cacheKey, imageProvider);
        }
      }
      
      // Strategy 3: Try downloaded/cached images
      imageProvider = await _tryLoadCachedImage(category);
      if (imageProvider != null) {
        LoggingService.info('Loaded cached image for category: $category');
        return _cacheAndReturn(cacheKey, imageProvider);
      }
      
      // Strategy 4: Generate programmatic image
      imageProvider = await _generateProgrammaticImage(category);
      LoggingService.info('Generated programmatic image for category: $category');
      return _cacheAndReturn(cacheKey, imageProvider);
      
    } catch (e) {
      LoggingService.error('Error loading image for $category: $e');
      
      // Ultimate fallback: Generate solid color image
      imageProvider = await _generateColorImage(_colorFallbacks[category] ?? _colorFallbacks['default']!);
      return _cacheAndReturn(cacheKey, imageProvider);
    }
  }

  /// Try to load asset with error handling
  Future<ImageProvider?> _tryLoadAsset(String assetPath) async {
    try {
      // Check if asset exists in bundle
      await rootBundle.load(assetPath);
      return AssetImage(assetPath);
    } catch (e) {
      LoggingService.warning('Asset not found: $assetPath');
      return null;
    }
  }

  /// Try to load cached/downloaded image
  Future<ImageProvider?> _tryLoadCachedImage(String category) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = '${directory.path}/images/${category}_cached.jpg';
      final file = File(imagePath);
      
      if (await file.exists()) {
        return FileImage(file);
      }
    } catch (e) {
      LoggingService.warning('Failed to load cached image: $e');
    }
    return null;
  }

  /// Generate programmatic image based on category
  Future<ImageProvider> _generateProgrammaticImage(String category) async {
    try {
      // Create a gradient image programmatically
      final color = _colorFallbacks[category] ?? _colorFallbacks['default']!;
      final bytes = await _createGradientImage(color);
      return MemoryImage(bytes);
    } catch (e) {
      LoggingService.error('Failed to generate programmatic image: $e');
      return await _generateColorImage(_colorFallbacks['default']!);
    }
  }

  /// Generate solid color image
  Future<ImageProvider> _generateColorImage(Color color) async {
    final bytes = await _createSolidColorImage(color);
    return MemoryImage(bytes);
  }

  /// Create gradient image bytes
  Future<Uint8List> _createGradientImage(Color baseColor) async {
    // This would typically use a canvas to create a gradient
    // For now, return a solid color as fallback
    return _createSolidColorImage(baseColor);
  }

  /// Create solid color image bytes
  Future<Uint8List> _createSolidColorImage(Color color) async {
    // Create a simple 100x100 colored image
    const width = 100;
    const height = 100;
    final bytes = Uint8List(width * height * 4); // RGBA
    
    for (int i = 0; i < bytes.length; i += 4) {
      bytes[i] = color.red;     // R
      bytes[i + 1] = color.green; // G
      bytes[i + 2] = color.blue;  // B
      bytes[i + 3] = color.alpha; // A
    }
    
    return bytes;
  }

  /// Cache and return image
  ImageProvider _cacheAndReturn(String cacheKey, ImageProvider imageProvider) {
    _imageCache[cacheKey] = imageProvider;
    return imageProvider;
  }

  /// Preload images for better performance
  Future<void> preloadImages(BuildContext context, List<String> categories) async {
    for (final category in categories) {
      try {
        final imageProvider = await getImage(category: category);
        await precacheImage(imageProvider, context);
        LoggingService.info('Preloaded image for category: $category');
      } catch (e) {
        LoggingService.warning('Failed to preload image for $category: $e');
      }
    }
  }

  /// Clear cache to free memory
  void clearCache() {
    _imageCache.clear();
    _assetPathCache.clear();
    LoggingService.info('Asset cache cleared');
  }

  /// Get asset info for debugging
  Map<String, dynamic> getAssetInfo() {
    return {
      'cached_images': _imageCache.length,
      'cached_paths': _assetPathCache.length,
      'available_categories': _assetFallbacks.keys.toList(),
      'fallback_colors': _colorFallbacks.keys.toList(),
    };
  }
}

/// Widget that uses dynamic asset manager
class DynamicAssetImage extends StatelessWidget {
  final String category;
  final String? specificAsset;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const DynamicAssetImage({
    super.key,
    required this.category,
    this.specificAsset,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ImageProvider>(
      future: DynamicAssetManager.instance.getImage(
        category: category,
        specificAsset: specificAsset,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ?? 
            Container(
              width: width,
              height: height,
              color: Colors.grey[300],
              child: const Center(child: CircularProgressIndicator()),
            );
        }
        
        if (snapshot.hasError || !snapshot.hasData) {
          return errorWidget ?? 
            Container(
              width: width,
              height: height,
              color: Colors.grey[400],
              child: const Icon(Icons.error, color: Colors.white),
            );
        }
        
        return Image(
          image: snapshot.data!,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            return errorWidget ?? 
              Container(
                width: width,
                height: height,
                color: Colors.grey[400],
                child: const Icon(Icons.broken_image, color: Colors.white),
              );
          },
        );
      },
    );
  }
}
