import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quote_model.dart';
import 'logging_service.dart';

/// Service that ensures the app works perfectly in guest mode with rich local data
class GuestFirstService {
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _guestQuotesKey = 'guest_quotes_cache';
  static const String _guestCategoriesKey = 'guest_categories_cache';
  static const String _guestPreferencesKey = 'guest_preferences';
  static const String _appInitializedKey = 'app_initialized';

  static GuestFirstService? _instance;
  static SharedPreferences? _prefs;

  GuestFirstService._();

  static Future<GuestFirstService> getInstance() async {
    _instance ??= GuestFirstService._();
    _prefs ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  /// Initialize the app for guest mode with rich local data
  Future<bool> initializeGuestMode() async {
    try {
      LoggingService.info('Initializing guest mode...');

      // Check if this is first launch
      final isFirstLaunch = _prefs!.getBool(_isFirstLaunchKey) ?? true;
      
      if (isFirstLaunch) {
        await _setupFirstLaunch();
      }

      // Ensure we have local quotes and categories
      await _ensureLocalData();
      
      // Set app as initialized
      await _prefs!.setBool(_appInitializedKey, true);
      
      LoggingService.info('Guest mode initialized successfully');
      return true;
    } catch (e) {
      LoggingService.error('Failed to initialize guest mode: $e');
      return false;
    }
  }

  /// Setup data for first app launch
  Future<void> _setupFirstLaunch() async {
    LoggingService.info('Setting up first launch...');
    
    // Set default preferences
    await _setDefaultPreferences();
    
    // Cache initial quotes
    await _cacheInitialQuotes();
    
    // Cache categories
    await _cacheCategories();
    
    // Mark first launch as complete
    await _prefs!.setBool(_isFirstLaunchKey, false);
    
    LoggingService.info('First launch setup complete');
  }

  /// Set default user preferences for guest mode
  Future<void> _setDefaultPreferences() async {
    final defaultPrefs = {
      'selectedCategories': ['Motivation', 'Inspiration', 'Success'],
      'notificationEnabled': true,
      'notificationTimes': ['09:00', '18:00'],
      'darkModeEnabled': false,
      'language': 'en',
      'onboardingComplete': false,
      'widgetEnabled': true,
      'widgetUpdateInterval': 60, // minutes
      'offlineMode': true,
    };
    
    await _prefs!.setString(_guestPreferencesKey, jsonEncode(defaultPrefs));
  }

  /// Cache initial quotes for offline use
  Future<void> _cacheInitialQuotes() async {
    final initialQuotes = [
      {
        'id': 1,
        'text': 'The only way to do great work is to love what you do.',
        'author': 'Steve Jobs',
        'category': 'Motivation',
        'tags': ['work', 'passion', 'success'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 2,
        'text': 'Life is what happens to you while you\'re busy making other plans.',
        'author': 'John Lennon',
        'category': 'Life',
        'tags': ['life', 'planning', 'wisdom'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 3,
        'text': 'The future belongs to those who believe in the beauty of their dreams.',
        'author': 'Eleanor Roosevelt',
        'category': 'Dreams',
        'tags': ['dreams', 'future', 'belief'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 4,
        'text': 'It is during our darkest moments that we must focus to see the light.',
        'author': 'Aristotle',
        'category': 'Inspiration',
        'tags': ['hope', 'perseverance', 'light'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 5,
        'text': 'Success is not final, failure is not fatal: it is the courage to continue that counts.',
        'author': 'Winston Churchill',
        'category': 'Success',
        'tags': ['success', 'failure', 'courage'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 6,
        'text': 'The only impossible journey is the one you never begin.',
        'author': 'Tony Robbins',
        'category': 'Motivation',
        'tags': ['journey', 'beginning', 'possibility'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 7,
        'text': 'In the middle of difficulty lies opportunity.',
        'author': 'Albert Einstein',
        'category': 'Opportunity',
        'tags': ['difficulty', 'opportunity', 'wisdom'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 8,
        'text': 'Believe you can and you're halfway there.',
        'author': 'Theodore Roosevelt',
        'category': 'Belief',
        'tags': ['belief', 'confidence', 'achievement'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 9,
        'text': 'The best time to plant a tree was 20 years ago. The second best time is now.',
        'author': 'Chinese Proverb',
        'category': 'Action',
        'tags': ['action', 'timing', 'wisdom'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 10,
        'text': 'Your limitation—it\'s only your imagination.',
        'author': 'Unknown',
        'category': 'Limitation',
        'tags': ['limitation', 'imagination', 'potential'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 11,
        'text': 'Great things never come from comfort zones.',
        'author': 'Anonymous',
        'category': 'Growth',
        'tags': ['comfort zone', 'growth', 'challenge'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 12,
        'text': 'Dream it. Wish it. Do it.',
        'author': 'Anonymous',
        'category': 'Dreams',
        'tags': ['dreams', 'action', 'achievement'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 13,
        'text': 'Success doesn\'t just find you. You have to go out and get it.',
        'author': 'Anonymous',
        'category': 'Success',
        'tags': ['success', 'effort', 'action'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 14,
        'text': 'The harder you work for something, the greater you\'ll feel when you achieve it.',
        'author': 'Anonymous',
        'category': 'Achievement',
        'tags': ['hard work', 'achievement', 'satisfaction'],
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 15,
        'text': 'Dream bigger. Do bigger.',
        'author': 'Anonymous',
        'category': 'Dreams',
        'tags': ['dreams', 'ambition', 'action'],
        'createdAt': DateTime.now().toIso8601String(),
      },
    ];
    
    await _prefs!.setString(_guestQuotesKey, jsonEncode(initialQuotes));
  }

  /// Cache available categories
  Future<void> _cacheCategories() async {
    final categories = [
      'Motivation',
      'Inspiration',
      'Success',
      'Life',
      'Dreams',
      'Opportunity',
      'Belief',
      'Action',
      'Limitation',
      'Growth',
      'Achievement',
    ];
    
    await _prefs!.setString(_guestCategoriesKey, jsonEncode(categories));
  }

  /// Ensure we have local data available
  Future<void> _ensureLocalData() async {
    // Check if we have cached quotes
    final quotesJson = _prefs!.getString(_guestQuotesKey);
    if (quotesJson == null || quotesJson.isEmpty) {
      await _cacheInitialQuotes();
    }
    
    // Check if we have cached categories
    final categoriesJson = _prefs!.getString(_guestCategoriesKey);
    if (categoriesJson == null || categoriesJson.isEmpty) {
      await _cacheCategories();
    }
  }

  /// Get cached quotes for offline use
  Future<List<Map<String, dynamic>>> getCachedQuotes() async {
    final quotesJson = _prefs!.getString(_guestQuotesKey);
    if (quotesJson == null) {
      await _cacheInitialQuotes();
      return getCachedQuotes();
    }
    
    try {
      final List<dynamic> quotesData = jsonDecode(quotesJson);
      return quotesData.cast<Map<String, dynamic>>();
    } catch (e) {
      LoggingService.error('Error parsing cached quotes: $e');
      await _cacheInitialQuotes();
      return getCachedQuotes();
    }
  }

  /// Get cached categories
  Future<List<String>> getCachedCategories() async {
    final categoriesJson = _prefs!.getString(_guestCategoriesKey);
    if (categoriesJson == null) {
      await _cacheCategories();
      return getCachedCategories();
    }
    
    try {
      final List<dynamic> categoriesData = jsonDecode(categoriesJson);
      return categoriesData.cast<String>();
    } catch (e) {
      LoggingService.error('Error parsing cached categories: $e');
      await _cacheCategories();
      return getCachedCategories();
    }
  }

  /// Get guest preferences
  Future<Map<String, dynamic>> getGuestPreferences() async {
    final prefsJson = _prefs!.getString(_guestPreferencesKey);
    if (prefsJson == null) {
      await _setDefaultPreferences();
      return getGuestPreferences();
    }
    
    try {
      return jsonDecode(prefsJson) as Map<String, dynamic>;
    } catch (e) {
      LoggingService.error('Error parsing guest preferences: $e');
      await _setDefaultPreferences();
      return getGuestPreferences();
    }
  }

  /// Update guest preferences
  Future<void> updateGuestPreferences(Map<String, dynamic> preferences) async {
    await _prefs!.setString(_guestPreferencesKey, jsonEncode(preferences));
  }

  /// Check if app is initialized
  Future<bool> isAppInitialized() async {
    return _prefs!.getBool(_appInitializedKey) ?? false;
  }

  /// Check if this is first launch
  Future<bool> isFirstLaunch() async {
    return _prefs!.getBool(_isFirstLaunchKey) ?? true;
  }

  /// Reset guest data (for testing or user request)
  Future<void> resetGuestData() async {
    await _prefs!.remove(_isFirstLaunchKey);
    await _prefs!.remove(_guestQuotesKey);
    await _prefs!.remove(_guestCategoriesKey);
    await _prefs!.remove(_guestPreferencesKey);
    await _prefs!.remove(_appInitializedKey);
    
    // Reinitialize
    await initializeGuestMode();
  }
}
