import 'package:dartz/dartz.dart';
import '../../domain/entities/quote.dart';
import '../../domain/repositories/quote_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/services/logging_service.dart';
import '../datasources/quote_remote_datasource.dart';
import '../datasources/backend_quote_datasource.dart';

/// Offline-first quote repository that ensures users always have quotes available
/// Priority: Local storage > Fallback quotes > Backend API
class OfflineFirstQuoteRepository implements QuoteRepository {
  final QuoteRemoteDataSource localDataSource;
  final BackendQuoteDataSource? backendDataSource;

  OfflineFirstQuoteRepository({
    required this.localDataSource,
    this.backendDataSource,
  });

  @override
  Future<Either<Failure, Quote>> getRandomQuote(List<String> categories) async {
    try {
      LoggingService.info('Getting random quote for categories: $categories');

      // Try local data source first (includes cached quotes and fallbacks)
      try {
        final quote = await localDataSource.getRandomQuote(categories);
        LoggingService.info('Quote retrieved from local source');
        return Right(quote);
      } catch (e) {
        LoggingService.warning('Local source failed: $e');
      }

      // Try backend if available and local fails
      if (backendDataSource != null) {
        try {
          final categoryIds = categories.map((c) => c.hashCode).toList();
          final quote = await backendDataSource!.getRandomQuote(categoryIds: categoryIds);
          LoggingService.info('Quote retrieved from backend');
          return Right(quote);
        } catch (e) {
          LoggingService.warning('Backend source failed: $e');
        }
      }

      // Final fallback - try local source again with empty categories
      try {
        final quote = await localDataSource.getRandomQuote([]);
        LoggingService.info('Using fallback quote from local source');
        return Right(quote);
      } catch (e) {
        LoggingService.error('All quote sources failed: $e');
        return const Left(NetworkFailure(message: 'Unable to load quotes. Please check your connection.'));
      }
    } catch (e) {
      LoggingService.error('Unexpected error: $e');
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getQuotesByCategory(String category) async {
    try {
      // Try local first
      try {
        final quotes = await localDataSource.getQuotesByCategory(category);
        if (quotes.isNotEmpty) {
          return Right(quotes);
        }
      } catch (e) {
        LoggingService.warning('Local category quotes failed: $e');
      }

      // Try backend
      if (backendDataSource != null) {
        try {
          final quotes = await backendDataSource!.getQuotes(categoryId: category.hashCode);
          if (quotes.isNotEmpty) {
            return Right(quotes);
          }
        } catch (e) {
          LoggingService.warning('Backend category quotes failed: $e');
        }
      }

      // Final fallback - return empty list
      return const Right([]);

    } catch (e) {
      LoggingService.error('Error getting quotes by category: $e');
      return const Right([]);
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableCategories() async {
    try {
      // Try local first
      try {
        final categories = await localDataSource.getAvailableCategories();
        if (categories.isNotEmpty) {
          return Right(categories);
        }
      } catch (e) {
        LoggingService.warning('Local categories failed: $e');
      }

      // Try backend
      if (backendDataSource != null) {
        try {
          final categories = await backendDataSource!.getCategories();
          final categoryNames = categories.map((c) => c.name).toList();
          if (categoryNames.isNotEmpty) {
            return Right(categoryNames);
          }
        } catch (e) {
          LoggingService.warning('Backend categories failed: $e');
        }
      }

      // Fallback categories
      const fallbackCategories = ['Motivation', 'Inspiration', 'Success', 'Life', 'Dreams'];
      return const Right(fallbackCategories);

    } catch (e) {
      // Always return fallback categories
      const fallbackCategories = ['Motivation', 'Inspiration', 'Success', 'Life', 'Dreams'];
      return const Right(fallbackCategories);
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> searchQuotes(String query) async {
    try {
      // Try local first
      try {
        final quotes = await localDataSource.searchQuotes(query);
        if (quotes.isNotEmpty) {
          return Right(quotes);
        }
      } catch (e) {
        LoggingService.warning('Local search failed: $e');
      }

      // Try backend
      if (backendDataSource != null) {
        try {
          final quotes = await backendDataSource!.getQuotes(search: query);
          if (quotes.isNotEmpty) {
            return Right(quotes);
          }
        } catch (e) {
          LoggingService.warning('Backend search failed: $e');
        }
      }

      // Return empty list if no results found
      return const Right([]);

    } catch (e) {
      LoggingService.error('Error searching quotes: $e');
      return const Right([]);
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getFavoriteQuotes() async {
    // This would typically load from local storage
    // For guest mode, return empty list
    return const Right([]);
  }

  @override
  Future<Either<Failure, void>> addToFavorites(String quoteId) async {
    // For guest mode, this would save to local storage
    // For now, just return success
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> removeFromFavorites(String quoteId) async {
    // For guest mode, this would remove from local storage
    // For now, just return success
    return const Right(null);
  }

  @override
  Future<Either<Failure, Quote>> getQuoteById(String id) async {
    // Try backend first
    if (backendDataSource != null) {
      try {
        final quote = await backendDataSource!.getQuoteById(int.parse(id));
        return Right(quote);
      } catch (e) {
        LoggingService.warning('Backend getQuoteById failed: $e');
      }
    }

    // Fallback - try to get a random quote
    try {
      final quote = await localDataSource.getRandomQuote([]);
      return Right(quote);
    } catch (e) {
      return const Left(NetworkFailure(message: 'Quote not found'));
    }
  }

  @override
  Future<Either<Failure, void>> cacheQuotes(List<Quote> quotes) async {
    // This would cache quotes locally
    // For now, just return success
    return const Right(null);
  }

  @override
  Future<Either<Failure, List<Quote>>> getCachedQuotes() async {
    // Try to get cached quotes from local data source
    try {
      final quotes = await localDataSource.getQuotesByCategory('');
      return Right(quotes);
    } catch (e) {
      return const Right([]);
    }
  }

  @override
  Future<Either<Failure, void>> clearCache() async {
    // This would clear local cache
    // For now, just return success
    return const Right(null);
  }
}
