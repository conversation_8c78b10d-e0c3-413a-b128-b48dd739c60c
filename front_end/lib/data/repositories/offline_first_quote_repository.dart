import 'dart:math';
import 'package:dartz/dartz.dart';
import '../../domain/entities/quote.dart';
import '../../domain/repositories/quote_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/services/logging_service.dart';
import '../datasources/quote_remote_datasource.dart';
import '../datasources/backend_quote_datasource.dart';
import '../models/quote_model.dart';

/// Offline-first quote repository that ensures users always have quotes available
/// Priority: Local storage > Fallback quotes > Backend API
class OfflineFirstQuoteRepository implements QuoteRepository {
  final QuoteRemoteDataSource localDataSource;
  final BackendQuoteDataSource? backendDataSource;
  
  // Built-in fallback quotes for complete offline experience
  static final List<QuoteModel> _fallbackQuotes = [
    QuoteModel(
      id: 1,
      text: "The only way to do great work is to love what you do.",
      author: "<PERSON>",
      category: "Motivation",
      tags: ["work", "passion", "success"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 2,
      text: "Life is what happens to you while you're busy making other plans.",
      author: "<PERSON>",
      category: "Life",
      tags: ["life", "planning", "wisdom"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 3,
      text: "The future belongs to those who believe in the beauty of their dreams.",
      author: "<PERSON> Roosevelt",
      category: "Dreams",
      tags: ["dreams", "future", "belief"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 4,
      text: "It is during our darkest moments that we must focus to see the light.",
      author: "Aristotle",
      category: "Inspiration",
      tags: ["hope", "perseverance", "light"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 5,
      text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
      author: "Winston Churchill",
      category: "Success",
      tags: ["success", "failure", "courage"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 6,
      text: "The only impossible journey is the one you never begin.",
      author: "Tony Robbins",
      category: "Motivation",
      tags: ["journey", "beginning", "possibility"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 7,
      text: "In the middle of difficulty lies opportunity.",
      author: "Albert Einstein",
      category: "Opportunity",
      tags: ["difficulty", "opportunity", "wisdom"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 8,
      text: "Believe you can and you're halfway there.",
      author: "Theodore Roosevelt",
      category: "Belief",
      tags: ["belief", "confidence", "achievement"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 9,
      text: "The best time to plant a tree was 20 years ago. The second best time is now.",
      author: "Chinese Proverb",
      category: "Action",
      tags: ["action", "timing", "wisdom"],
      createdAt: DateTime.now(),
    ),
    QuoteModel(
      id: 10,
      text: "Your limitation—it's only your imagination.",
      author: "Unknown",
      category: "Limitation",
      tags: ["limitation", "imagination", "potential"],
      createdAt: DateTime.now(),
    ),
  ];

  OfflineFirstQuoteRepository({
    required this.localDataSource,
    this.backendDataSource,
  });

  @override
  Future<Either<Failure, Quote>> getRandomQuote(List<String> categories) async {
    try {
      LoggingService.info('Getting random quote for categories: $categories');
      
      // Try local data source first (includes cached quotes)
      try {
        final quote = await localDataSource.getRandomQuote(categories);
        LoggingService.info('Quote retrieved from local source');
        return Right(quote);
      } catch (e) {
        LoggingService.warning('Local source failed: $e');
      }

      // Try backend if available and local fails
      if (backendDataSource != null) {
        try {
          final categoryIds = categories.map((c) => c.hashCode).toList();
          final quote = await backendDataSource!.getRandomQuote(categoryIds: categoryIds);
          LoggingService.info('Quote retrieved from backend');
          return Right(quote);
        } catch (e) {
          LoggingService.warning('Backend source failed: $e');
        }
      }

      // Fallback to built-in quotes
      final quote = _getRandomFallbackQuote(categories);
      LoggingService.info('Using fallback quote');
      return Right(quote);
      
    } catch (e) {
      LoggingService.error('All quote sources failed: $e');
      // Even if everything fails, return a fallback quote
      final quote = _getRandomFallbackQuote(categories);
      return Right(quote);
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getQuotesByCategory(String category) async {
    try {
      // Try local first
      try {
        final quotes = await localDataSource.getQuotesByCategory(category);
        if (quotes.isNotEmpty) {
          return Right(quotes);
        }
      } catch (e) {
        LoggingService.warning('Local category quotes failed: $e');
      }

      // Try backend
      if (backendDataSource != null) {
        try {
          final quotes = await backendDataSource!.getQuotes(categoryId: category.hashCode);
          if (quotes.isNotEmpty) {
            return Right(quotes);
          }
        } catch (e) {
          LoggingService.warning('Backend category quotes failed: $e');
        }
      }

      // Fallback to built-in quotes
      final fallbackQuotes = _fallbackQuotes
          .where((q) => q.category.toLowerCase() == category.toLowerCase())
          .toList();
      
      return Right(fallbackQuotes.isNotEmpty ? fallbackQuotes : _fallbackQuotes);
      
    } catch (e) {
      // Always return something
      final fallbackQuotes = _fallbackQuotes
          .where((q) => q.category.toLowerCase() == category.toLowerCase())
          .toList();
      return Right(fallbackQuotes.isNotEmpty ? fallbackQuotes : _fallbackQuotes);
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableCategories() async {
    try {
      // Try local first
      try {
        final categories = await localDataSource.getAvailableCategories();
        if (categories.isNotEmpty) {
          return Right(categories);
        }
      } catch (e) {
        LoggingService.warning('Local categories failed: $e');
      }

      // Try backend
      if (backendDataSource != null) {
        try {
          final categories = await backendDataSource!.getCategories();
          final categoryNames = categories.map((c) => c.name).toList();
          if (categoryNames.isNotEmpty) {
            return Right(categoryNames);
          }
        } catch (e) {
          LoggingService.warning('Backend categories failed: $e');
        }
      }

      // Fallback categories
      final categories = _fallbackQuotes.map((q) => q.category).toSet().toList();
      return Right(categories);
      
    } catch (e) {
      // Always return fallback categories
      final categories = _fallbackQuotes.map((q) => q.category).toSet().toList();
      return Right(categories);
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> searchQuotes(String query) async {
    try {
      // Try local first
      try {
        final quotes = await localDataSource.searchQuotes(query);
        if (quotes.isNotEmpty) {
          return Right(quotes);
        }
      } catch (e) {
        LoggingService.warning('Local search failed: $e');
      }

      // Try backend
      if (backendDataSource != null) {
        try {
          final quotes = await backendDataSource!.getQuotes(search: query);
          if (quotes.isNotEmpty) {
            return Right(quotes);
          }
        } catch (e) {
          LoggingService.warning('Backend search failed: $e');
        }
      }

      // Fallback search in built-in quotes
      final searchResults = _fallbackQuotes
          .where((quote) =>
              quote.text.toLowerCase().contains(query.toLowerCase()) ||
              quote.author.toLowerCase().contains(query.toLowerCase()) ||
              quote.category.toLowerCase().contains(query.toLowerCase()))
          .toList();
      
      return Right(searchResults);
      
    } catch (e) {
      // Always return something, even if empty
      return const Right([]);
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getFavoriteQuotes() async {
    // This would typically load from local storage
    // For guest mode, return empty list
    return const Right([]);
  }

  /// Get a random fallback quote, optionally filtered by categories
  Quote _getRandomFallbackQuote(List<String> categories) {
    List<QuoteModel> availableQuotes = _fallbackQuotes;
    
    if (categories.isNotEmpty) {
      final filteredQuotes = _fallbackQuotes
          .where((quote) => categories.any((cat) => 
              quote.category.toLowerCase().contains(cat.toLowerCase())))
          .toList();
      
      if (filteredQuotes.isNotEmpty) {
        availableQuotes = filteredQuotes;
      }
    }
    
    final random = Random();
    return availableQuotes[random.nextInt(availableQuotes.length)];
  }
}
