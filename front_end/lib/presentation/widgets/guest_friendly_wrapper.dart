import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../../core/services/logging_service.dart';

/// Wrapper widget that ensures all screens work perfectly in guest mode
/// Shows appropriate UI based on authentication state without blocking access
class Guest<PERSON>riendlyWrapper extends StatelessWidget {
  final Widget child;
  final String screenName;
  final bool showGuestBanner;
  final VoidCallback? onLoginPressed;

  const GuestFriendlyWrapper({
    super.key,
    required this.child,
    required this.screenName,
    this.showGuestBanner = false,
    this.onLoginPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        LoggingService.info('$screenName accessed in ${authProvider.isGuest ? 'guest' : 'authenticated'} mode');
        
        // Always show the child - no authentication barriers
        return Column(
          children: [
            // Optional guest banner for features that benefit from login
            if (showGuestBanner && authProvider.isGuest)
              _buildGuestBanner(context),
            
            // Main content - always accessible
            Expanded(child: child),
          ],
        );
      },
    );
  }

  Widget _buildGuestBanner(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).primaryColor.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'You\'re using guest mode. Sign in to sync your data across devices.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          if (onLoginPressed != null) ...[
            const SizedBox(width: 8),
            TextButton(
              onPressed: onLoginPressed,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                'Sign In',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Mixin for screens to easily implement guest-friendly behavior
mixin GuestFriendlyScreen<T extends StatefulWidget> on State<T> {
  
  /// Check if user is in guest mode
  bool get isGuestMode {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.isGuest;
  }
  
  /// Check if user can use the app (always true - guest or authenticated)
  bool get canUseApp {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.canUseApp;
  }
  
  /// Show a snackbar with guest mode information
  void showGuestModeInfo(String message, {VoidCallback? onLoginPressed}) {
    if (!isGuestMode) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        action: onLoginPressed != null
            ? SnackBarAction(
                label: 'Sign In',
                textColor: Colors.white,
                onPressed: onLoginPressed,
              )
            : null,
        backgroundColor: Theme.of(context).primaryColor,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }
  
  /// Handle feature that benefits from authentication but works in guest mode
  void handleOptionalAuthFeature({
    required VoidCallback guestAction,
    required VoidCallback authenticatedAction,
    String? guestMessage,
  }) {
    if (isGuestMode) {
      if (guestMessage != null) {
        showGuestModeInfo(guestMessage, onLoginPressed: () {
          // Navigate to login screen
          Navigator.of(context).pushNamed('/login');
        });
      }
      guestAction();
    } else {
      authenticatedAction();
    }
  }
  
  /// Get appropriate storage key for guest vs authenticated user
  String getStorageKey(String baseKey) {
    return isGuestMode ? 'guest_$baseKey' : 'user_$baseKey';
  }
  
  /// Show dialog explaining guest mode limitations (if any)
  void showGuestModeDialog({
    required String title,
    required String message,
    VoidCallback? onLoginPressed,
    VoidCallback? onContinueAsGuest,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.person_outline, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          if (onContinueAsGuest != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onContinueAsGuest();
              },
              child: const Text('Continue as Guest'),
            ),
          if (onLoginPressed != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onLoginPressed();
              },
              child: const Text('Sign In'),
            ),
        ],
      ),
    );
  }
}

/// Widget that shows different content based on auth state without blocking access
class AuthStateBuilder extends StatelessWidget {
  final Widget guestWidget;
  final Widget authenticatedWidget;
  final Widget? loadingWidget;

  const AuthStateBuilder({
    super.key,
    required this.guestWidget,
    required this.authenticatedWidget,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        if (authProvider.isLoading) {
          return loadingWidget ?? const Center(child: CircularProgressIndicator());
        }
        
        // Always show appropriate content - never block access
        return authProvider.isGuest ? guestWidget : authenticatedWidget;
      },
    );
  }
}

/// Button that adapts its behavior based on authentication state
class AdaptiveActionButton extends StatelessWidget {
  final String guestLabel;
  final String authenticatedLabel;
  final VoidCallback guestAction;
  final VoidCallback authenticatedAction;
  final IconData? icon;
  final bool showGuestInfo;
  final String? guestInfoMessage;

  const AdaptiveActionButton({
    super.key,
    required this.guestLabel,
    required this.authenticatedLabel,
    required this.guestAction,
    required this.authenticatedAction,
    this.icon,
    this.showGuestInfo = false,
    this.guestInfoMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final isGuest = authProvider.isGuest;
        final label = isGuest ? guestLabel : authenticatedLabel;
        final action = isGuest ? guestAction : authenticatedAction;
        
        return ElevatedButton.icon(
          onPressed: () {
            if (isGuest && showGuestInfo && guestInfoMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(guestInfoMessage!),
                  action: SnackBarAction(
                    label: 'Sign In',
                    onPressed: () => Navigator.of(context).pushNamed('/login'),
                  ),
                ),
              );
            }
            action();
          },
          icon: icon != null ? Icon(icon) : null,
          label: Text(label),
        );
      },
    );
  }
}
